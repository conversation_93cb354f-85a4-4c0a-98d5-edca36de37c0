{"absolute_value": 0, "align_labels_right": 0, "creation": "2025-09-26 11:22:32.323123", "custom_format": 1, "default_print_language": "en", "disabled": 0, "doc_type": "DPR", "docstatus": 0, "doctype": "Print Format", "font_size": 14, "html": "<!DOCTYPE html>\n<html>\n<head>\n    <style>\n        body {\n            font-family: Arial, sans-serif;\n            font-size: 12px;\n            margin: 0;\n            padding: 20px;\n        }\n        .header {\n            text-align: center;\n            font-weight: bold;\n            font-size: 16px;\n            margin-bottom: 20px;\n            text-decoration: underline;\n        }\n        .info-section {\n            margin-bottom: 20px;\n        }\n        .info-row {\n            display: flex;\n            margin-bottom: 5px;\n        }\n        .info-label {\n            font-weight: bold;\n            width: 120px;\n        }\n        table {\n            width: 100%;\n            border-collapse: collapse;\n            margin-bottom: 20px;\n        }\n        th, td {\n            border: 1px solid black;\n            padding: 5px;\n            text-align: center;\n            vertical-align: middle;\n        }\n        th {\n            background-color: #f0f0f0;\n            font-weight: bold;\n        }\n        .section-title {\n            font-weight: bold;\n            text-align: center;\n            margin: 20px 0 10px 0;\n            text-decoration: underline;\n        }\n        .work-status-container {\n            display: flex;\n            gap: 20px;\n            margin-bottom: 20px;\n        }\n        .work-status-left, .work-status-right {\n            flex: 1;\n        }\n        .work-status-table {\n            width: 100%;\n        }\n        .equipment-material-container {\n            display: flex;\n            gap: 20px;\n            margin-bottom: 20px;\n        }\n        .equipment-section, .material-section {\n            flex: 1;\n        }\n        .material-status {\n            margin-top: 20px;\n        }\n        .material-status h4 {\n            font-weight: bold;\n            text-decoration: underline;\n            margin-bottom: 10px;\n        }\n        .text-left {\n            text-align: left;\n        }\n        .no-border {\n            border: none;\n        }\n        .hold-ups-section, .request-info-section {\n            margin: 20px 0;\n        }\n        .hold-ups-section h4, .request-info-section h4 {\n            font-weight: bold;\n            text-decoration: underline;\n            text-align: center;\n            margin-bottom: 10px;\n        }\n    </style>\n</head>\n<body>\n    <!-- Header -->\n    <div class=\"header\">\n        YOGI BUILDCON DAILY PROGRESS REPORT\n    </div>\n    \n    <!-- Basic Information -->\n    <div class=\"info-section\">\n        <div class=\"info-row\">\n            <span class=\"info-label\">Client Name :</span>\n            <span>{{ doc.client }}</span>\n            <span style=\"margin-left: auto; font-weight: bold;\">Date : {{ doc.date }}</span>\n        </div>\n        <div class=\"info-row\">\n            <span class=\"info-label\">Project :</span>\n            <span>{{ doc.project }}</span>\n        </div>\n        <div class=\"info-row\">\n            <span class=\"info-label\">Address :</span>\n            <span>{{ doc.address }}</span>\n        </div>\n    </div>\n    \n    <!-- Manpower Strength Report -->\n    <div class=\"section-title\">Manpower strength report</div>\n    <table>\n        <thead>\n            <tr>\n                <th>No</th>\n                <th>Description</th>\n                <th>Prev</th>\n                <th>Today</th>\n                <th>Specialized</th>\n                <th>Carp</th>\n                <th>Fitt</th>\n                <th>B. Mas</th>\n                <th>P.Mas</th>\n                <th>M/C</th>\n            </tr>\n        </thead>\n        <tbody>\n            {% for row in doc.manpower_table %}\n            <tr>\n                <td>{{ loop.index }}</td>\n                <td class=\"text-left\">{{ row.description or '' }}</td>\n                <td>{{ row.prev or 0 }}</td>\n                <td>{{ row.today or 0 }}</td>\n                <td>{{ row.specialized or 0 }}</td>\n                <td>{{ row.carp or 0 }}</td>\n                <td>{{ row.fitt or 0 }}</td>\n                <td>{{ row.b_mas or 0 }}</td>\n                <td>{{ row.pmas or 0 }}</td>\n                <td>{{ row.mc or 0 }}</td>\n            </tr>\n            {% endfor %}\n            <tr style=\"font-weight: bold;\">\n                <td colspan=\"2\">Total</td>\n                <td>{{ doc.total_prev or 0 }}</td>\n                <td>{{ doc.total_today or 0 }}</td>\n                <td>{{ doc.total_spec or 0 }}</td>\n                <td>{{ doc.total_carp or 0 }}</td>\n                <td>{{ doc.total_fitt or 0 }}</td>\n                <td>{{ doc.total_b_mas or 0 }}</td>\n                <td>{{ doc.total_p_mas or 0 }}</td>\n                <td>{{ doc.total_mc or 0 }}</td>\n            </tr>\n        </tbody>\n    </table>\n    \n    <!-- Work Status -->\n    <div class=\"section-title\">Work Status</div>\n    <div class=\"work-status-container\">\n        <div class=\"work-status-left\">\n            <table class=\"work-status-table\">\n                <thead>\n                    <tr>\n                        <th>No</th>\n                        <th>Work Done Today</th>\n                    </tr>\n                </thead>\n                <tbody>\n                    {% for row in doc.wdone_today_table %}\n                    <tr>\n                        <td>{{ loop.index }}</td>\n                        <td class=\"text-left\">{{ row.work_done_today or '' }}</td>\n                    </tr>\n                    {% endfor %}\n                </tbody>\n            </table>\n        </div>\n        <div class=\"work-status-right\">\n            <table class=\"work-status-table\">\n                <thead>\n                    <tr>\n                        <th>No</th>\n                        <th>Work Planned Tomorrow</th>\n                    </tr>\n                </thead>\n                <tbody>\n                    {% for row in doc.wplanned_tomm_table %}\n                    <tr>\n                        <td>{{ loop.index }}</td>\n                        <td class=\"text-left\">{{ row.work_planned_tommorow or '' }}</td>\n                    </tr>\n                    {% endfor %}\n                </tbody>\n            </table>\n        </div>\n    </div>\n    \n    <!-- Hold Ups / Stoppages -->\n    <div class=\"hold-ups-section\">\n        <h4>Hold Ups /Stoppages of works with reasons</h4>\n        <div>{{ doc.hold_ups_stoppages_of_works_with_reasons or 'None' }}</div>\n    </div>\n    \n    <!-- Request for Information -->\n    <div class=\"request-info-section\">\n        <h4>Request for information</h4>\n        <div>{{ doc.request_for_information or 'None' }}</div>\n    </div>\n    \n    <!-- Equipment and Material Section -->\n    <div class=\"equipment-material-container\">\n        <div class=\"equipment-section\">\n            <h4 style=\"font-weight: bold; text-decoration: underline; text-align: center; margin-bottom: 10px;\">Equipment's/Machinery Deployed</h4>\n            <table>\n                <thead>\n                    <tr>\n                        <th>No</th>\n                        <th>Machinery Deployed</th>\n                        <th>No(s)</th>\n                    </tr>\n                </thead>\n                <tbody>\n                    {% for row in doc.mach_deployed_table %}\n                    <tr>\n                        <td>{{ loop.index }}</td>\n                        <td class=\"text-left\">{{ row.machinery_deployed or '' }}</td>\n                        <td>{{ row.nos or '' }}</td>\n                    </tr>\n                    {% endfor %}\n                </tbody>\n            </table>\n        </div>\n        \n        <div class=\"material-section\">\n            <h4 style=\"font-weight: bold; text-decoration: underline; text-align: center; margin-bottom: 10px;\">Today's Material Receipt</h4>\n            <table>\n                <thead>\n                    <tr>\n                        <th>No</th>\n                        <th>Material</th>\n                        <th>Qty</th>\n                        <th>Desc</th>\n                        <th>Remark</th>\n                    </tr>\n                </thead>\n                <tbody>\n                    {% for row in doc.mat_recp_table %}\n                    <tr>\n                        <td>{{ loop.index }}</td>\n                        <td class=\"text-left\">{{ row.material or '' }}</td>\n                        <td>{{ row.qty or '' }}</td>\n                        <td class=\"text-left\">{{ row.desc or '' }}</td>\n                        <td class=\"text-left\">{{ row.remark or '' }}</td>\n                    </tr>\n                    {% endfor %}\n                </tbody>\n            </table>\n        </div>\n    </div>\n    \n    <!-- Status of Material -->\n    <div class=\"material-status\">\n        <h4>Status of Material :</h4>\n        <div>{{ doc.status_of_material or '' }}</div>\n    </div>\n    \n</body>\n</html>", "idx": 0, "line_breaks": 0, "margin_bottom": 15.0, "margin_left": 15.0, "margin_right": 15.0, "margin_top": 15.0, "modified": "2025-09-26 11:22:32.323123", "modified_by": "Administrator", "module": "Buildo Frappe", "name": "DPR Custom", "owner": "Administrator", "page_number": "<PERSON>de", "pdf_generator": "wkhtmltopdf", "print_designer": 0, "print_designer_template_app": "print_designer", "print_format_builder": 0, "print_format_builder_beta": 0, "print_format_for": "DocType", "print_format_type": "<PERSON><PERSON>", "raw_printing": 0, "show_section_headings": 0, "standard": "Yes"}